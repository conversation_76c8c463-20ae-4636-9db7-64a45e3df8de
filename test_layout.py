#!/usr/bin/env python3
"""
测试页面布局
"""

import requests
from bs4 import BeautifulSoup

def test_layout():
    """测试任务详情页面布局"""
    print("=== 测试页面布局 ===\n")
    
    try:
        # 获取任务详情页面
        url = "http://localhost:5011/task-detail?computer=服务器A&file=文件夹1&runid=1000"
        response = requests.get(url)
        
        if response.status_code == 200:
            print("✅ 页面访问成功")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查页面结构
            sections = []
            
            # 查找所有卡片
            cards = soup.find_all('div', class_='card')
            
            for i, card in enumerate(cards):
                header = card.find('div', class_='card-header')
                if header:
                    title_element = header.find('h5')
                    if title_element:
                        title = title_element.get_text().strip()
                        sections.append((i+1, title))
            
            print(f"\n📋 页面结构（共{len(sections)}个部分）:")
            for order, title in sections:
                print(f"  {order}. {title}")
            
            # 检查趋势图是否在统计汇总之前
            trend_position = None
            summary_position = None
            
            for order, title in sections:
                if "运行趋势图" in title:
                    trend_position = order
                elif "统计汇总" in title:
                    summary_position = order
            
            print(f"\n🔍 布局检查:")
            if trend_position and summary_position:
                if trend_position < summary_position:
                    print(f"  ✅ 趋势图位置正确：第{trend_position}位（统计汇总第{summary_position}位）")
                else:
                    print(f"  ❌ 趋势图位置错误：第{trend_position}位（统计汇总第{summary_position}位）")
            else:
                print(f"  ❌ 未找到趋势图或统计汇总部分")
                print(f"     趋势图位置: {trend_position}")
                print(f"     统计汇总位置: {summary_position}")
            
            # 检查ECharts相关元素
            print(f"\n📊 趋势图组件检查:")
            
            # 检查ECharts脚本
            echarts_script = soup.find('script', src=lambda x: x and 'echarts' in x)
            if echarts_script:
                print("  ✅ ECharts库正确引入")
            else:
                print("  ❌ ECharts库未找到")
            
            # 检查图表容器
            chart_container = soup.find('div', id='trend-chart')
            if chart_container:
                print("  ✅ 图表容器存在")
                
                # 检查容器高度
                style = chart_container.get('style', '')
                if 'height' in style:
                    print(f"  ✅ 图表高度已设置: {style}")
                else:
                    print("  ⚠️ 图表高度未设置")
            else:
                print("  ❌ 图表容器不存在")
            
            # 检查切换按钮
            area_btn = soup.find('button', id='btn-area')
            line_btn = soup.find('button', id='btn-line')
            
            if area_btn and line_btn:
                print("  ✅ 图表切换按钮存在")
            else:
                print("  ❌ 图表切换按钮缺失")
            
        else:
            print(f"❌ 页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    print("\n=== 布局测试完成 ===")

if __name__ == '__main__':
    test_layout()
