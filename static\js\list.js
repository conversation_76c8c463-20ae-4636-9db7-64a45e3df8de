// 列表页面JavaScript

// 全局变量
let currentPage = 1;
let currentPerPage = 20;
let currentSort = 'StartTime';
let currentSortOrder = 'desc';
let currentFilters = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadFilterOptions();
    loadTasks();
    
    // 绑定事件
    bindEvents();
});

// 初始化页面
function initializePage() {
    // 从URL参数恢复状态
    const urlParams = new URLSearchParams(window.location.search);
    currentPage = parseInt(urlParams.get('page')) || 1;
    currentPerPage = parseInt(urlParams.get('per_page')) || 20;
    currentSort = urlParams.get('sort_by') || 'StartTime';
    currentSortOrder = urlParams.get('sort_order') || 'desc';
    
    // 恢复筛选条件
    document.getElementById('job_name').value = urlParams.get('job_name') || '';
    document.getElementById('start_time_from').value = urlParams.get('start_time_from') || '';
    document.getElementById('start_time_to').value = urlParams.get('start_time_to') || '';
    document.getElementById('computer').value = urlParams.get('computer') || '';
    document.getElementById('file').value = urlParams.get('file') || '';
    document.getElementById('per_page').value = currentPerPage;
}

// 绑定事件
function bindEvents() {
    // 筛选表单提交
    document.getElementById('filter-form').addEventListener('submit', function(e) {
        e.preventDefault();
        applyFilters();
    });
    
    // 每页显示数量变化
    document.getElementById('per_page').addEventListener('change', function() {
        currentPerPage = parseInt(this.value);
        currentPage = 1; // 重置到第一页
        updateUrlAndLoad();
    });
    
    // 表头排序点击
    document.querySelectorAll('.sortable').forEach(header => {
        header.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            if (currentSort === sortBy) {
                currentSortOrder = currentSortOrder === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort = sortBy;
                currentSortOrder = 'desc';
            }
            updateSortHeaders();
            updateUrlAndLoad();
        });
    });
}

// 加载筛选选项
function loadFilterOptions() {
    apiRequest('/filters/options')
        .then(response => {
            if (response.success) {
                populateFilterOptions(response.data);
            } else {
                console.error('Failed to load filter options:', response.message);
            }
        })
        .catch(error => {
            console.error('Error loading filter options:', error);
        });
}

// 填充筛选选项
function populateFilterOptions(data) {
    // 填充服务器选项
    const computerSelect = document.getElementById('computer');
    const currentComputer = computerSelect.value;
    computerSelect.innerHTML = '<option value="">全部服务器</option>';
    data.computers.forEach(computer => {
        const option = document.createElement('option');
        option.value = computer;
        option.textContent = computer;
        if (computer === currentComputer) {
            option.selected = true;
        }
        computerSelect.appendChild(option);
    });
    
    // 填充文件夹选项
    const fileSelect = document.getElementById('file');
    const currentFile = fileSelect.value;
    fileSelect.innerHTML = '<option value="">全部文件夹</option>';
    data.files.forEach(file => {
        const option = document.createElement('option');
        option.value = file;
        option.textContent = file;
        if (file === currentFile) {
            option.selected = true;
        }
        fileSelect.appendChild(option);
    });
}

// 应用筛选条件
function applyFilters() {
    currentFilters = {
        job_name: document.getElementById('job_name').value.trim(),
        start_time_from: document.getElementById('start_time_from').value,
        start_time_to: document.getElementById('start_time_to').value,
        computer: document.getElementById('computer').value,
        file: document.getElementById('file').value
    };
    
    currentPage = 1; // 重置到第一页
    updateUrlAndLoad();
}

// 重置筛选条件
function resetFilters() {
    document.getElementById('filter-form').reset();
    currentFilters = {};
    currentPage = 1;
    currentPerPage = 20;
    document.getElementById('per_page').value = currentPerPage;
    updateUrlAndLoad();
}

// 更新URL并加载数据
function updateUrlAndLoad() {
    updateUrl();
    loadTasks();
}

// 更新URL参数
function updateUrl() {
    const url = new URL(window.location);
    
    // 设置分页参数
    url.searchParams.set('page', currentPage);
    url.searchParams.set('per_page', currentPerPage);
    url.searchParams.set('sort_by', currentSort);
    url.searchParams.set('sort_order', currentSortOrder);
    
    // 设置筛选参数
    Object.keys(currentFilters).forEach(key => {
        if (currentFilters[key]) {
            url.searchParams.set(key, currentFilters[key]);
        } else {
            url.searchParams.delete(key);
        }
    });
    
    window.history.replaceState({}, '', url);
}

// 加载任务列表
function loadTasks() {
    showLoading('loading-tasks');
    hideElement('empty-tasks');
    
    // 构建查询参数
    const params = new URLSearchParams({
        page: currentPage,
        per_page: currentPerPage,
        sort_by: currentSort,
        sort_order: currentSortOrder,
        ...currentFilters
    });
    
    apiRequest('/tasks?' + params.toString())
        .then(response => {
            if (response.success) {
                renderTasks(response.data.tasks);
                renderPagination(response.data.pagination);
                updateTaskCount(response.data.pagination.total);
            } else {
                showToast('加载任务列表失败: ' + response.message, 'danger');
                showEmptyState();
            }
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
            showToast('加载任务列表失败', 'danger');
            showEmptyState();
        })
        .finally(() => {
            hideLoading('loading-tasks');
        });
}

// 渲染任务列表
function renderTasks(tasks) {
    const tbody = document.getElementById('tasks-tbody');

    if (tasks.length === 0) {
        showEmptyState();
        return;
    }

    const tasksHtml = tasks.map(task => `
        <tr>
            <td>
                <div class="fw-bold">${task.JobName || '-'}</div>
                <small class="text-muted">ID: ${task.JobId || '-'}</small>
            </td>
            <td class="text-center">
                <span class="badge bg-success">${formatNumber(task.UrlSuccessCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-danger">${formatNumber(task.UrlFailCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-warning">${formatNumber(task.UrlRepeatCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-primary">${formatNumber(task.ContentSuccessCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-secondary">${formatNumber(task.ContentFailCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-info">${formatNumber(task.OutputSuccessCount)}</span>
            </td>
            <td class="text-center">
                <span class="badge bg-dark">${formatNumber(task.OutputFailCount)}</span>
            </td>
            <td>
                <div>${formatDateTime(task.StartTime)}</div>
            </td>
            <td>
                <div>${formatDateTime(task.EndTime)}</div>
            </td>
            <td>
                <div class="text-primary fw-bold">${task.time_since_start || '-'}</div>
            </td>
            <td>
                <span class="badge bg-light text-dark">${task.computer || '-'}</span>
            </td>
            <td>
                <span class="badge bg-light text-dark">${task.file || '-'}</span>
            </td>
            <td class="text-center">
                <button class="btn btn-sm btn-primary" onclick="viewTaskDetail('${encodeURIComponent(task.computer)}', '${encodeURIComponent(task.file)}', ${task.runid})" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = tasksHtml;
}

// 显示空状态
function showEmptyState() {
    document.getElementById('tasks-tbody').innerHTML = '';
    showElement('empty-tasks');
}

// 更新任务数量显示
function updateTaskCount(total) {
    const taskCount = document.getElementById('task-count');
    taskCount.textContent = `共 ${formatNumber(total)} 条记录`;
}

// 更新排序表头
function updateSortHeaders() {
    // 清除所有排序样式
    document.querySelectorAll('.sortable').forEach(header => {
        header.classList.remove('asc', 'desc');
    });

    // 添加当前排序样式
    const currentHeader = document.querySelector(`[data-sort="${currentSort}"]`);
    if (currentHeader) {
        currentHeader.classList.add(currentSortOrder);
    }
}

// 渲染分页
function renderPagination(pagination) {
    const paginationNav = document.getElementById('pagination-nav');

    if (pagination.pages <= 1) {
        paginationNav.innerHTML = '';
        return;
    }

    let paginationHtml = '<ul class="pagination justify-content-center">';

    // 上一页
    if (pagination.has_prev) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${pagination.prev_num})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
    } else {
        paginationHtml += `
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-left"></i>
                </span>
            </li>
        `;
    }

    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);

    if (startPage > 1) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(1)">1</a>
            </li>
        `;
        if (startPage > 2) {
            paginationHtml += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        if (i === pagination.page) {
            paginationHtml += `
                <li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>
            `;
        } else {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="goToPage(${i})">${i}</a>
                </li>
            `;
        }
    }

    if (endPage < pagination.pages) {
        if (endPage < pagination.pages - 1) {
            paginationHtml += `
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `;
        }
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${pagination.pages})">${pagination.pages}</a>
            </li>
        `;
    }

    // 下一页
    if (pagination.has_next) {
        paginationHtml += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="goToPage(${pagination.next_num})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    } else {
        paginationHtml += `
            <li class="page-item disabled">
                <span class="page-link">
                    <i class="fas fa-chevron-right"></i>
                </span>
            </li>
        `;
    }

    paginationHtml += '</ul>';
    paginationNav.innerHTML = paginationHtml;
}

// 跳转到指定页面
function goToPage(page) {
    currentPage = page;
    updateUrlAndLoad();
}

// 导出数据
function exportData() {
    // 构建查询参数（不包含分页）
    const params = new URLSearchParams({
        per_page: 10000, // 导出所有数据
        sort_by: currentSort,
        sort_order: currentSortOrder,
        ...currentFilters
    });

    showToast('正在准备导出数据...', 'info');

    apiRequest('/tasks?' + params.toString())
        .then(response => {
            if (response.success && response.data.tasks.length > 0) {
                const exportData = response.data.tasks.map(task => ({
                    '任务名称': task.JobName || '',
                    '任务ID': task.JobId || '',
                    '成功网址数': task.UrlSuccessCount || '',
                    '失败网址数': task.UrlFailCount || '',
                    '重复网址数': task.UrlRepeatCount || '',
                    '内容成功数': task.ContentSuccessCount || '',
                    '内容失败数': task.ContentFailCount || '',
                    '发布成功数': task.OutputSuccessCount || '',
                    '发布失败数': task.OutputFailCount || '',
                    '开始时间': formatDateTime(task.StartTime),
                    '结束时间': formatDateTime(task.EndTime),
                    '距今时间': task.time_since_start || '',
                    '服务器': task.computer || '',
                    '文件夹': task.file || ''
                }));

                const filename = `采集任务列表_${formatDate(new Date().toISOString())}.csv`;
                exportToCSV(exportData, filename);
            } else {
                showToast('没有数据可导出', 'warning');
            }
        })
        .catch(error => {
            console.error('Error exporting data:', error);
            showToast('导出数据失败', 'danger');
        });
}

// 查看任务详情
function viewTaskDetail(computer, file, runid) {
    // 解码URL编码的参数
    const decodedComputer = decodeURIComponent(computer);
    const decodedFile = decodeURIComponent(file);

    // 构建详情页面URL
    const params = new URLSearchParams({
        computer: decodedComputer,
        file: decodedFile,
        runid: runid
    });

    // 跳转到详情页面
    window.location.href = '/task-detail?' + params.toString();
}
