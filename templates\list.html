{% extends "base.html" %}

{% block title %}任务列表 - 采集任务运行结果统计平台{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 text-primary">
                <i class="fas fa-list me-2"></i>
                采集任务列表
            </h1>
            <p class="text-muted">查看和管理所有采集任务的运行结果</p>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>
                        筛选条件
                    </h5>
                </div>
                <div class="card-body">
                    <form id="filter-form">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="job_name" class="form-label">任务名称</label>
                                <input type="text" class="form-control" id="job_name" name="job_name" 
                                       placeholder="输入任务名称">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="start_time_from" class="form-label">开始时间（从）</label>
                                <input type="date" class="form-control" id="start_time_from" name="start_time_from">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="start_time_to" class="form-label">开始时间（到）</label>
                                <input type="date" class="form-control" id="start_time_to" name="start_time_to">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="computer" class="form-label">服务器</label>
                                <select class="form-select" id="computer" name="computer">
                                    <option value="">全部服务器</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label for="file" class="form-label">文件夹</label>
                                <select class="form-select" id="file" name="file">
                                    <option value="">全部文件夹</option>
                                </select>
                            </div>
                            <div class="col-md-1 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <label for="per_page" class="form-label">每页显示</label>
                                <select class="form-select" id="per_page" name="per_page">
                                    <option value="20">20条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                            <div class="col-md-10 mb-3 d-flex align-items-end">
                                <button type="button" class="btn btn-outline-secondary me-2" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>重置
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="exportData()">
                                    <i class="fas fa-download me-1"></i>导出
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        任务列表
                    </h5>
                    <div id="task-count" class="text-muted">
                        <!-- 任务数量统计 -->
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="tasks-table">
                            <thead class="table-light">
                                <tr>
                                    <th class="sortable" data-sort="JobName">
                                        任务名称 <i class="fas fa-sort"></i>
                                    </th>
                                    <th>成功网址数</th>
                                    <th>失败网址数</th>
                                    <th>重复网址数</th>
                                    <th>内容成功数</th>
                                    <th>内容失败数</th>
                                    <th>发布成功数</th>
                                    <th>发布失败数</th>
                                    <th class="sortable" data-sort="StartTime">
                                        开始时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="EndTime">
                                        结束时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th class="sortable" data-sort="time_since_start">
                                        距今时间 <i class="fas fa-sort"></i>
                                    </th>
                                    <th>服务器</th>
                                    <th>文件夹</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasks-tbody">
                                <!-- 任务数据将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 加载状态 -->
                    <div id="loading-tasks" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载任务数据...</p>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="empty-tasks" class="text-center py-5" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无数据</h5>
                        <p class="text-muted">没有找到符合条件的任务记录</p>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="card-footer bg-light">
                    <nav id="pagination-nav">
                        <!-- 分页组件将通过JavaScript生成 -->
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/list.js') }}"></script>
{% endblock %}
