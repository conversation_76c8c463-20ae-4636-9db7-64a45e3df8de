from database import db
from datetime import datetime
from sqlalchemy import func

class CollectionTask(db.Model):
    """采集任务运行结果模型"""
    
    __tablename__ = 'collection_tasks'
    
    # 主键
    id = db.Column(db.Integer, primary_key=True, comment='主键')
    
    # 任务信息
    runid = db.Column(db.Integer, comment='采集任务id')
    JobName = db.Column(db.String(255), comment='采集任务名字')
    JobId = db.Column(db.Integer, comment='采集任务id')
    
    # 统计数据
    UrlSuccessCount = db.Column(db.String(50), comment='成功网址数')
    UrlFailCount = db.Column(db.String(50), comment='失败网址数')
    UrlRepeatCount = db.Column(db.String(50), comment='重复网址数')
    ContentSuccessCount = db.Column(db.String(50), comment='内容成功数')
    ContentFailCount = db.Column(db.String(50), comment='内容失败数')
    OutputSuccessCount = db.Column(db.String(50), comment='发布成功数')
    OutputFailCount = db.Column(db.String(50), comment='发布失败数')
    
    # 时间信息
    EndTime = db.Column(db.DateTime, comment='结束时间')
    StartTime = db.Column(db.DateTime, comment='开始时间')
    collecttime = db.Column(db.TIMESTAMP, default=datetime.utcnow, comment='收集时间')
    
    # 服务器信息
    computer = db.Column(db.String(255), comment='服务器')
    file = db.Column(db.String(255), comment='文件夹')
    
    # 排重和提醒
    paichong = db.Column(db.String(500), comment='排重 任务id + 服务器 + 文件夹名')
    fabu = db.Column(db.Integer, default=0, comment='未提醒0，已提醒1')
    
    def __repr__(self):
        return f'<CollectionTask {self.JobName}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'runid': self.runid,
            'JobName': self.JobName,
            'JobId': self.JobId,
            'UrlSuccessCount': self.UrlSuccessCount,
            'UrlFailCount': self.UrlFailCount,
            'UrlRepeatCount': self.UrlRepeatCount,
            'ContentSuccessCount': self.ContentSuccessCount,
            'ContentFailCount': self.ContentFailCount,
            'OutputSuccessCount': self.OutputSuccessCount,
            'OutputFailCount': self.OutputFailCount,
            'EndTime': self.EndTime.isoformat() if self.EndTime else None,
            'StartTime': self.StartTime.isoformat() if self.StartTime else None,
            'collecttime': self.collecttime.isoformat() if self.collecttime else None,
            'computer': self.computer,
            'file': self.file,
            'paichong': self.paichong,
            'fabu': self.fabu,
            'time_since_start': self.get_time_since_start()
        }
    
    def get_time_since_start(self):
        """计算开始时间距离当前时间的差值"""
        if not self.StartTime:
            return None
        
        now = datetime.now()
        diff = now - self.StartTime
        
        days = diff.days
        hours, remainder = divmod(diff.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        if days > 0:
            return f"{days}天{hours}小时前"
        elif hours > 0:
            return f"{hours}小时{minutes}分钟前"
        else:
            return f"{minutes}分钟前"
    
    @staticmethod
    def get_statistics():
        """获取统计数据"""
        # 总采集站点数（根据paichong去重）
        total_sites = db.session.query(func.count(func.distinct(CollectionTask.paichong))).scalar() or 0
        
        # 当天已采集数
        today = datetime.now().date()
        today_collected = db.session.query(CollectionTask).filter(
            func.date(CollectionTask.StartTime) == today
        ).count()
        
        # 未采集统计（假设fabu=0表示未采集完成）
        not_collected = db.session.query(CollectionTask).filter(
            CollectionTask.fabu == 0
        ).count()
        
        return {
            'total_sites': total_sites,
            'today_collected': today_collected,
            'not_collected': not_collected
        }
