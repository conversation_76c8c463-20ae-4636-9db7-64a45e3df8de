{% extends "base.html" %}

{% block title %}首页统计 - 采集任务运行结果统计平台{% endblock %}

{% block content %}
<div class="container">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h2 text-primary">
                <i class="fas fa-chart-pie me-2"></i>
                采集任务运行结果统计
            </h1>
            <p class="text-muted">实时监控采集任务运行状态和统计数据</p>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-5" id="statistics-cards">
        <!-- 加载中状态 -->
        <div class="col-12 text-center" id="loading-stats">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载统计数据...</p>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <a href="{{ url_for('task_list') }}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-list me-2"></i>
                                查看任务列表
                            </a>
                        </div>
                        <div class="col-md-6 mb-3">
                            <button class="btn btn-outline-primary btn-lg w-100" onclick="refreshStatistics()">
                                <i class="fas fa-sync-alt me-2"></i>
                                刷新统计数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近任务预览 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        最近任务
                    </h5>
                    <a href="{{ url_for('task_list') }}" class="btn btn-sm btn-outline-primary">
                        查看全部
                    </a>
                </div>
                <div class="card-body" id="recent-tasks">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载最近任务...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/index.js') }}"></script>
{% endblock %}
