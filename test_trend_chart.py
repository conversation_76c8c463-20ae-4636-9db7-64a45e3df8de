#!/usr/bin/env python3
"""
测试趋势图功能
"""

import requests
import json
from datetime import datetime

def test_trend_chart():
    """测试趋势图数据"""
    base_url = "http://localhost:5011"
    
    print("=== 测试趋势图功能 ===\n")
    
    # 测试任务详情API，获取趋势图数据
    print("1. 测试任务详情API（趋势图数据）...")
    try:
        # 测试第一个任务（应该有150条记录）
        detail_url = f"{base_url}/api/task-detail?computer=服务器A&file=文件夹1&runid=1000&limit=50"
        response = requests.get(detail_url)
        
        if response.status_code == 200:
            data = response.json()
            if data['success']:
                summary = data['data']['summary']
                tasks = data['data']['tasks']
                
                print(f"✓ 获取任务详情成功")
                print(f"  任务名称: {summary['job_name']}")
                print(f"  总运行次数: {summary['total_runs']}")
                print(f"  当前显示: {summary['displayed_runs']} 条记录")
                
                # 分析趋势图数据
                print(f"\n2. 分析趋势图数据...")
                
                # 按时间排序
                sorted_tasks = sorted(tasks, key=lambda x: x['StartTime'])
                
                print(f"  时间范围: {sorted_tasks[0]['StartTime']} 到 {sorted_tasks[-1]['StartTime']}")
                
                # 统计各项数据的范围
                url_success_values = [int(task['UrlSuccessCount'] or 0) for task in tasks]
                url_fail_values = [int(task['UrlFailCount'] or 0) for task in tasks]
                content_success_values = [int(task['ContentSuccessCount'] or 0) for task in tasks]
                
                print(f"  成功网址数范围: {min(url_success_values)} - {max(url_success_values)}")
                print(f"  失败网址数范围: {min(url_fail_values)} - {max(url_fail_values)}")
                print(f"  内容成功数范围: {min(content_success_values)} - {max(content_success_values)}")
                
                # 显示前5条记录的详细数据
                print(f"\n  前5条记录详情:")
                for i, task in enumerate(sorted_tasks[:5]):
                    start_time = datetime.fromisoformat(task['StartTime'].replace('Z', '+00:00')) if task['StartTime'] else None
                    formatted_time = start_time.strftime('%m/%d %H:%M') if start_time else 'N/A'
                    
                    print(f"    #{i+1} {formatted_time}: 成功网址{task['UrlSuccessCount']}, 失败网址{task['UrlFailCount']}, 内容成功{task['ContentSuccessCount']}")
                
                # 验证数据完整性
                print(f"\n3. 验证数据完整性...")
                complete_records = 0
                for task in tasks:
                    if all([
                        task['UrlSuccessCount'] is not None,
                        task['UrlFailCount'] is not None,
                        task['UrlRepeatCount'] is not None,
                        task['ContentSuccessCount'] is not None,
                        task['ContentFailCount'] is not None,
                        task['OutputSuccessCount'] is not None,
                        task['OutputFailCount'] is not None,
                        task['StartTime'] is not None
                    ]):
                        complete_records += 1
                
                print(f"  完整记录数: {complete_records}/{len(tasks)}")
                print(f"  数据完整率: {(complete_records/len(tasks)*100):.1f}%")
                
                if complete_records == len(tasks):
                    print("  ✓ 所有记录数据完整，适合生成趋势图")
                else:
                    print("  ⚠️ 部分记录数据不完整，可能影响趋势图显示")
                
            else:
                print(f"✗ 获取任务详情失败: {data['message']}")
        else:
            print(f"✗ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 测试页面访问
    print(f"\n4. 测试趋势图页面访问...")
    try:
        detail_page_url = f"{base_url}/task-detail?computer=服务器A&file=文件夹1&runid=1000"
        page_response = requests.get(detail_page_url)
        
        if page_response.status_code == 200:
            page_content = page_response.text
            
            # 检查ECharts库是否正确引入
            if 'echarts.min.js' in page_content:
                print("  ✓ ECharts库正确引入")
            else:
                print("  ✗ ECharts库未正确引入")
            
            # 检查趋势图容器是否存在
            if 'trend-chart' in page_content:
                print("  ✓ 趋势图容器存在")
            else:
                print("  ✗ 趋势图容器不存在")
            
            # 检查图表切换按钮是否存在
            if 'switchChartType' in page_content:
                print("  ✓ 图表切换功能存在")
            else:
                print("  ✗ 图表切换功能不存在")
                
            print("  ✓ 趋势图页面访问正常")
            
        else:
            print(f"  ✗ 页面访问失败: {page_response.status_code}")
            
    except Exception as e:
        print(f"  ✗ 页面访问测试失败: {str(e)}")
    
    print("\n=== 趋势图功能测试完成 ===")
    print("\n📊 趋势图功能说明:")
    print("- 支持堆叠面积图和折线图两种显示模式")
    print("- 显示7个数据系列：成功网址数、失败网址数、重复网址数、内容成功数、内容失败数、发布成功数、发布失败数")
    print("- 支持数据缩放、保存图片等交互功能")
    print("- 按时间顺序显示任务运行趋势")

if __name__ == '__main__':
    test_trend_chart()
