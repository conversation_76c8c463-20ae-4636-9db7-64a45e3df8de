from flask import Blueprint, request, jsonify
from database import db
from sqlalchemy import or_, and_, func, desc, asc
from datetime import datetime
from config import Config

# 创建蓝图
api_bp = Blueprint('api', __name__)

# 导入模型（避免循环导入）
from models import CollectionTask

@api_bp.route('/statistics', methods=['GET'])
def get_statistics():
    """获取首页统计数据"""
    try:
        stats = CollectionTask.get_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        }), 500

@api_bp.route('/tasks', methods=['GET'])
def get_tasks():
    """获取采集任务列表"""
    try:
        # 获取查询参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', Config.ITEMS_PER_PAGE_DEFAULT, type=int)
        
        # 验证per_page参数
        if per_page not in Config.ITEMS_PER_PAGE_OPTIONS:
            per_page = Config.ITEMS_PER_PAGE_DEFAULT
        
        # 筛选参数
        job_name = request.args.get('job_name', '').strip()
        start_time_from = request.args.get('start_time_from', '').strip()
        start_time_to = request.args.get('start_time_to', '').strip()
        computer = request.args.get('computer', '').strip()
        file_folder = request.args.get('file', '').strip()
        
        # 排序参数
        sort_by = request.args.get('sort_by', 'StartTime')  # 默认按开始时间排序
        sort_order = request.args.get('sort_order', 'desc')  # 默认降序
        
        # 构建查询
        query = CollectionTask.query
        
        # 应用筛选条件
        if job_name:
            query = query.filter(CollectionTask.JobName.like(f'%{job_name}%'))
        
        if start_time_from:
            try:
                start_date = datetime.strptime(start_time_from, '%Y-%m-%d')
                query = query.filter(CollectionTask.StartTime >= start_date)
            except ValueError:
                pass
        
        if start_time_to:
            try:
                end_date = datetime.strptime(start_time_to, '%Y-%m-%d')
                query = query.filter(CollectionTask.StartTime <= end_date)
            except ValueError:
                pass
        
        if computer:
            query = query.filter(CollectionTask.computer.like(f'%{computer}%'))
        
        if file_folder:
            query = query.filter(CollectionTask.file.like(f'%{file_folder}%'))
        
        # 应用排序
        if sort_by in ['StartTime', 'EndTime']:
            sort_column = getattr(CollectionTask, sort_by)
            if sort_order == 'asc':
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))
        elif sort_by == 'time_since_start':
            # 按开始时间距离当前时间排序
            if sort_order == 'asc':
                query = query.order_by(asc(CollectionTask.StartTime))
            else:
                query = query.order_by(desc(CollectionTask.StartTime))
        
        # 分页查询
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # 转换数据格式
        tasks = [task.to_dict() for task in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'tasks': tasks,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next,
                    'prev_num': pagination.prev_num,
                    'next_num': pagination.next_num
                }
            }
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务列表失败: {str(e)}'
        }), 500

@api_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task_detail(task_id):
    """获取单个任务详情"""
    try:
        task = CollectionTask.query.get_or_404(task_id)
        return jsonify({
            'success': True,
            'data': task.to_dict()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务详情失败: {str(e)}'
        }), 500

@api_bp.route('/task-detail', methods=['GET'])
def get_task_detail_by_params():
    """根据computer、file、runid获取采集任务详细统计"""
    try:
        # 获取查询参数
        computer = request.args.get('computer', '').strip()
        file_folder = request.args.get('file', '').strip()
        runid = request.args.get('runid', type=int)
        limit = request.args.get('limit', 100, type=int)  # 默认限制100条

        if not all([computer, file_folder, runid]):
            return jsonify({
                'success': False,
                'message': '缺少必要参数：computer、file、runid'
            }), 400

        # 验证limit参数
        if limit <= 0 or limit > 1000:
            limit = 100

        # 先获取总记录数
        total_count = CollectionTask.query.filter(
            CollectionTask.computer == computer,
            CollectionTask.file == file_folder,
            CollectionTask.runid == runid
        ).count()

        # 查询该采集任务的运行记录（按开始时间倒序，取最新的limit条）
        tasks = CollectionTask.query.filter(
            CollectionTask.computer == computer,
            CollectionTask.file == file_folder,
            CollectionTask.runid == runid
        ).order_by(desc(CollectionTask.StartTime)).limit(limit).all()

        if not tasks:
            return jsonify({
                'success': False,
                'message': '未找到匹配的采集任务记录'
            }), 404

        # 为了计算准确的汇总统计，需要查询所有记录的统计数据
        all_tasks_for_stats = CollectionTask.query.filter(
            CollectionTask.computer == computer,
            CollectionTask.file == file_folder,
            CollectionTask.runid == runid
        ).all()

        # 计算汇总统计（基于所有记录）
        total_stats = {
            'total_runs': total_count,
            'displayed_runs': len(tasks),
            'total_url_success': sum(int(task.UrlSuccessCount or 0) for task in all_tasks_for_stats),
            'total_url_fail': sum(int(task.UrlFailCount or 0) for task in all_tasks_for_stats),
            'total_url_repeat': sum(int(task.UrlRepeatCount or 0) for task in all_tasks_for_stats),
            'total_content_success': sum(int(task.ContentSuccessCount or 0) for task in all_tasks_for_stats),
            'total_content_fail': sum(int(task.ContentFailCount or 0) for task in all_tasks_for_stats),
            'total_output_success': sum(int(task.OutputSuccessCount or 0) for task in all_tasks_for_stats),
            'total_output_fail': sum(int(task.OutputFailCount or 0) for task in all_tasks_for_stats),
            'first_run_time': all_tasks_for_stats[-1].StartTime.isoformat() if all_tasks_for_stats[-1].StartTime else None,
            'last_run_time': all_tasks_for_stats[0].StartTime.isoformat() if all_tasks_for_stats[0].StartTime else None,
            'job_name': tasks[0].JobName,
            'computer': computer,
            'file': file_folder,
            'runid': runid,
            'limit': limit,
            'is_limited': total_count > limit
        }

        # 转换任务列表（仅显示的记录）
        task_list = [task.to_dict() for task in tasks]

        return jsonify({
            'success': True,
            'data': {
                'summary': total_stats,
                'tasks': task_list
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取任务详情失败: {str(e)}'
        }), 500

@api_bp.route('/filters/options', methods=['GET'])
def get_filter_options():
    """获取筛选选项"""
    try:
        # 获取所有不重复的服务器和文件夹
        computers = db.session.query(CollectionTask.computer).distinct().filter(
            CollectionTask.computer.isnot(None)
        ).all()
        computers = [c[0] for c in computers if c[0]]

        files = db.session.query(CollectionTask.file).distinct().filter(
            CollectionTask.file.isnot(None)
        ).all()
        files = [f[0] for f in files if f[0]]

        return jsonify({
            'success': True,
            'data': {
                'computers': sorted(computers),
                'files': sorted(files),
                'per_page_options': Config.ITEMS_PER_PAGE_OPTIONS
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取筛选选项失败: {str(e)}'
        }), 500
