/* 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: 1px solid #e9ecef;
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stats-label {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

.stats-icon {
    font-size: 3rem;
    opacity: 0.3;
    position: absolute;
    right: 20px;
    top: 20px;
}

/* 表格样式 */
.table {
    font-size: 0.9rem;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    border-top: none;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
    white-space: nowrap;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* 可排序表头样式 */
.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
}

.sortable:hover {
    background-color: #e9ecef !important;
}

.sortable i {
    margin-left: 5px;
    opacity: 0.5;
    transition: all 0.2s ease;
}

.sortable.asc i:before {
    content: "\f0de";
    opacity: 1;
    color: #007bff;
}

.sortable.desc i:before {
    content: "\f0dd";
    opacity: 1;
    color: #007bff;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #007bff;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* 加载动画 */
.spinner-border {
    width: 2rem;
    height: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* 状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-success {
    background-color: #28a745;
}

.status-warning {
    background-color: #ffc107;
}

.status-danger {
    background-color: #dc3545;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
