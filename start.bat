@echo off
echo 启动采集任务统计平台...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
pip show flask >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查数据库是否初始化
if not exist "caiji.db" (
    echo 正在初始化数据库...
    python init_db.py
    if errorlevel 1 (
        echo 错误: 数据库初始化失败
        pause
        exit /b 1
    )
)

REM 启动应用
echo 启动应用...
echo 应用将在 http://localhost:5011 启动
echo 按 Ctrl+C 停止应用
echo.
python app.py
