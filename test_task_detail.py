#!/usr/bin/env python3
"""
测试任务详情功能
"""

import requests
import json
from datetime import datetime

def test_api():
    """测试API接口"""
    base_url = "http://localhost:5011"
    
    print("=== 测试任务详情功能 ===\n")
    
    # 1. 测试任务列表API
    print("1. 测试任务列表API...")
    try:
        response = requests.get(f"{base_url}/api/tasks?per_page=5")
        if response.status_code == 200:
            data = response.json()
            if data['success'] and data['data']['tasks']:
                tasks = data['data']['tasks']
                print(f"✓ 获取到 {len(tasks)} 条任务记录")
                
                # 选择第一个任务进行详情测试
                first_task = tasks[0]
                computer = first_task['computer']
                file_folder = first_task['file']
                runid = first_task['runid']
                
                print(f"  选择任务: {first_task['JobName']}")
                print(f"  服务器: {computer}")
                print(f"  文件夹: {file_folder}")
                print(f"  RunID: {runid}")
                
                # 2. 测试任务详情API
                print("\n2. 测试任务详情API...")
                detail_url = f"{base_url}/api/task-detail?computer={computer}&file={file_folder}&runid={runid}"
                detail_response = requests.get(detail_url)
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    if detail_data['success']:
                        summary = detail_data['data']['summary']
                        task_list = detail_data['data']['tasks']
                        
                        print(f"✓ 任务详情获取成功")
                        print(f"  任务名称: {summary['job_name']}")
                        print(f"  总运行次数: {summary['total_runs']}")
                        print(f"  当前显示: {summary['displayed_runs']}")
                        print(f"  是否限制: {summary['is_limited']}")
                        print(f"  限制数量: {summary['limit']}")
                        print(f"  总成功网址数: {summary['total_url_success']}")
                        print(f"  总失败网址数: {summary['total_url_fail']}")
                        print(f"  首次运行: {summary['first_run_time']}")
                        print(f"  最近运行: {summary['last_run_time']}")
                        print(f"  运行记录数: {len(task_list)}")
                        
                        # 显示运行记录
                        print("\n  运行记录:")
                        for i, task in enumerate(task_list[:3]):  # 只显示前3条
                            start_time = datetime.fromisoformat(task['StartTime'].replace('Z', '+00:00')) if task['StartTime'] else None
                            end_time = datetime.fromisoformat(task['EndTime'].replace('Z', '+00:00')) if task['EndTime'] else None
                            
                            duration = ""
                            if start_time and end_time:
                                diff = end_time - start_time
                                hours = diff.seconds // 3600
                                minutes = (diff.seconds % 3600) // 60
                                duration = f"{hours}小时{minutes}分钟"
                            
                            print(f"    #{i+1}: {task['StartTime']} -> {task['EndTime']} ({duration})")
                            print(f"         成功: {task['UrlSuccessCount']}, 失败: {task['UrlFailCount']}")
                        
                        if len(task_list) > 3:
                            print(f"    ... 还有 {len(task_list) - 3} 条记录")

                        # 测试限制功能
                        if summary['is_limited']:
                            print(f"\n  ✓ 限制功能正常工作，显示了 {summary['displayed_runs']} / {summary['total_runs']} 条记录")

                            # 测试加载更多记录
                            print("\n  测试加载更多记录...")
                            more_detail_url = f"{base_url}/api/task-detail?computer={computer}&file={file_folder}&runid={runid}&limit=200"
                            more_response = requests.get(more_detail_url)

                            if more_response.status_code == 200:
                                more_data = more_response.json()
                                if more_data['success']:
                                    more_summary = more_data['data']['summary']
                                    print(f"  ✓ 加载更多记录成功，现在显示 {more_summary['displayed_runs']} 条记录")
                                else:
                                    print(f"  ✗ 加载更多记录失败: {more_data['message']}")
                            else:
                                print(f"  ✗ 加载更多记录请求失败: {more_response.status_code}")
                        else:
                            print(f"\n  ✓ 记录数量较少，无需限制显示")

                    else:
                        print(f"✗ 任务详情获取失败: {detail_data['message']}")
                else:
                    print(f"✗ 任务详情API请求失败: {detail_response.status_code}")
                
            else:
                print("✗ 任务列表为空")
        else:
            print(f"✗ 任务列表API请求失败: {response.status_code}")
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
    
    # 3. 测试页面访问
    print("\n3. 测试页面访问...")
    try:
        # 测试任务列表页面
        list_response = requests.get(f"{base_url}/list")
        if list_response.status_code == 200:
            print("✓ 任务列表页面访问正常")
        else:
            print(f"✗ 任务列表页面访问失败: {list_response.status_code}")
        
        # 测试任务详情页面
        detail_page_url = f"{base_url}/task-detail?computer=服务器A&file=文件夹1&runid=1000"
        detail_page_response = requests.get(detail_page_url)
        if detail_page_response.status_code == 200:
            print("✓ 任务详情页面访问正常")
        else:
            print(f"✗ 任务详情页面访问失败: {detail_page_response.status_code}")
            
    except Exception as e:
        print(f"✗ 页面访问测试失败: {str(e)}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_api()
