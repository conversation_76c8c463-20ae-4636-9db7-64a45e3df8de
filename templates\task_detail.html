{% extends "base.html" %}

{% block title %}任务详情 - 采集任务运行结果统计平台{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 返回按钮 -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>返回任务列表
            </a>
        </div>
    </div>

    <!-- 任务基本信息 -->
    <div class="row mb-4" id="task-info-section">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        任务基本信息
                    </h5>
                </div>
                <div class="card-body" id="task-basic-info">
                    <!-- 加载中状态 -->
                    <div class="text-center" id="loading-basic-info">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载任务信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 趋势图表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        运行趋势图
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" onclick="switchChartType('area')" id="btn-area">
                            <i class="fas fa-chart-area me-1"></i>面积图
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="switchChartType('line')" id="btn-line">
                            <i class="fas fa-chart-line me-1"></i>折线图
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="trend-chart" style="height: 400px;">
                        <!-- 图表将通过JavaScript生成 -->
                        <div class="text-center py-5" id="chart-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="mt-2 text-muted">正在生成趋势图表...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计汇总 -->
    <div class="row mb-4" id="summary-section">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        统计汇总
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="summary-cards">
                        <!-- 统计卡片将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 运行历史记录 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        运行历史记录
                    </h5>
                    <div id="history-count" class="text-white">
                        <!-- 记录数量统计 -->
                    </div>
                </div>
                <!-- 限制提示 -->
                <div id="limit-notice" class="alert alert-info mb-0" style="display: none; border-radius: 0;">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="limit-text"></span>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="loadMoreRecords()">
                        <i class="fas fa-plus me-1"></i>加载更多
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="history-table">
                            <thead class="table-light">
                                <tr>
                                    <th>运行序号</th>
                                    <th>开始时间</th>
                                    <th>结束时间</th>
                                    <th>运行时长</th>
                                    <th>成功网址数</th>
                                    <th>失败网址数</th>
                                    <th>重复网址数</th>
                                    <th>内容成功数</th>
                                    <th>内容失败数</th>
                                    <th>发布成功数</th>
                                    <th>发布失败数</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="history-tbody">
                                <!-- 历史记录将通过JavaScript加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 加载状态 -->
                    <div id="loading-history" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载运行历史...</p>
                    </div>
                    
                    <!-- 空状态 -->
                    <div id="empty-history" class="text-center py-5" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">暂无运行记录</h5>
                        <p class="text-muted">该任务还没有运行记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
{% endblock %}

{% block extra_js %}
<!-- ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="{{ url_for('static', filename='js/task_detail.js') }}"></script>
{% endblock %}
