// 首页JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadStatistics();
    loadRecentTasks();
});

// 加载统计数据
function loadStatistics() {
    apiRequest('/statistics')
        .then(response => {
            if (response.success) {
                renderStatistics(response.data);
            } else {
                showToast('加载统计数据失败: ' + response.message, 'danger');
                showStatisticsError();
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
            showToast('加载统计数据失败', 'danger');
            showStatisticsError();
        });
}

// 渲染统计数据
function renderStatistics(data) {
    const statisticsCards = document.getElementById('statistics-cards');
    
    const cardsHtml = `
        <div class="col-md-4 mb-4">
            <div class="card stats-card h-100 position-relative">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <h3 class="stats-number">${formatNumber(data.total_sites)}</h3>
                            <p class="stats-label">总采集站点数</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-globe stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card stats-card success h-100 position-relative">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <h3 class="stats-number">${formatNumber(data.today_collected)}</h3>
                            <p class="stats-label">当天已采集数</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-check-circle stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card stats-card warning h-100 position-relative">
                <div class="card-body">
                    <div class="row">
                        <div class="col-8">
                            <h3 class="stats-number">${formatNumber(data.not_collected)}</h3>
                            <p class="stats-label">未采集统计</p>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-exclamation-triangle stats-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    statisticsCards.innerHTML = cardsHtml;
    hideLoading('loading-stats');
}

// 显示统计数据错误
function showStatisticsError() {
    const statisticsCards = document.getElementById('statistics-cards');
    statisticsCards.innerHTML = `
        <div class="col-12 text-center">
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载统计数据失败，请稍后重试
            </div>
        </div>
    `;
    hideLoading('loading-stats');
}

// 加载最近任务
function loadRecentTasks() {
    apiRequest('/tasks?per_page=5&sort_by=StartTime&sort_order=desc')
        .then(response => {
            if (response.success) {
                renderRecentTasks(response.data.tasks);
            } else {
                showToast('加载最近任务失败: ' + response.message, 'danger');
                showRecentTasksError();
            }
        })
        .catch(error => {
            console.error('Error loading recent tasks:', error);
            showToast('加载最近任务失败', 'danger');
            showRecentTasksError();
        });
}

// 渲染最近任务
function renderRecentTasks(tasks) {
    const recentTasksContainer = document.getElementById('recent-tasks');
    
    if (tasks.length === 0) {
        recentTasksContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-inbox fa-2x mb-3"></i>
                <p>暂无最近任务</p>
            </div>
        `;
        return;
    }
    
    const tasksHtml = tasks.map(task => `
        <div class="row border-bottom py-3">
            <div class="col-md-3">
                <h6 class="mb-1">${task.JobName || '-'}</h6>
                <small class="text-muted">${task.computer || '-'} / ${task.file || '-'}</small>
            </div>
            <div class="col-md-2 text-center">
                <span class="badge bg-success">${formatNumber(task.UrlSuccessCount)}</span>
                <div><small class="text-muted">成功网址</small></div>
            </div>
            <div class="col-md-2 text-center">
                <span class="badge bg-danger">${formatNumber(task.UrlFailCount)}</span>
                <div><small class="text-muted">失败网址</small></div>
            </div>
            <div class="col-md-2 text-center">
                <span class="badge bg-primary">${formatNumber(task.ContentSuccessCount)}</span>
                <div><small class="text-muted">内容成功</small></div>
            </div>
            <div class="col-md-3 text-end">
                <div><small class="text-muted">开始时间</small></div>
                <div>${formatDateTime(task.StartTime)}</div>
                <div><small class="text-primary">${task.time_since_start || '-'}</small></div>
            </div>
        </div>
    `).join('');
    
    recentTasksContainer.innerHTML = tasksHtml;
}

// 显示最近任务错误
function showRecentTasksError() {
    const recentTasksContainer = document.getElementById('recent-tasks');
    recentTasksContainer.innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            加载最近任务失败，请稍后重试
        </div>
    `;
}

// 刷新统计数据
function refreshStatistics() {
    const statisticsCards = document.getElementById('statistics-cards');
    statisticsCards.innerHTML = `
        <div class="col-12 text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在刷新统计数据...</p>
        </div>
    `;
    
    const recentTasksContainer = document.getElementById('recent-tasks');
    recentTasksContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在刷新最近任务...</p>
        </div>
    `;
    
    // 重新加载数据
    loadStatistics();
    loadRecentTasks();
    
    showToast('正在刷新数据...', 'info');
}
