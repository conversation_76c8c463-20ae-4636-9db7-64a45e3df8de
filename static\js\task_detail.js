// 任务详情页面JavaScript

// 全局变量
let taskData = null;
let currentLimit = 100;
let taskParams = null;
let trendChart = null;
let currentChartType = 'area';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL参数获取任务信息
    const urlParams = new URLSearchParams(window.location.search);
    const computer = urlParams.get('computer');
    const file = urlParams.get('file');
    const runid = urlParams.get('runid');

    if (!computer || !file || !runid) {
        showToast('缺少必要参数，请从任务列表页面进入', 'danger');
        setTimeout(() => {
            window.location.href = '/list';
        }, 2000);
        return;
    }

    // 保存任务参数
    taskParams = { computer, file, runid };

    // 加载任务详情
    loadTaskDetail(computer, file, runid, currentLimit);
});

// 加载任务详情
function loadTaskDetail(computer, file, runid, limit = 100) {
    const params = new URLSearchParams({
        computer: computer,
        file: file,
        runid: runid,
        limit: limit
    });

    return apiRequest('/task-detail?' + params.toString())
        .then(response => {
            if (response.success) {
                taskData = response.data;
                renderTaskDetail(taskData);
                return response.data;
            } else {
                showToast('加载任务详情失败: ' + response.message, 'danger');
                showErrorState();
                throw new Error(response.message);
            }
        })
        .catch(error => {
            console.error('Error loading task detail:', error);
            showToast('加载任务详情失败', 'danger');
            showErrorState();
            throw error;
        });
}

// 渲染任务详情
function renderTaskDetail(data) {
    renderBasicInfo(data.summary);
    renderSummaryCards(data.summary);
    renderHistoryTable(data.tasks);
    renderLimitNotice(data.summary);
    renderTrendChart(data.tasks);
    hideLoading('loading-basic-info');
    hideLoading('loading-history');
}

// 渲染基本信息
function renderBasicInfo(summary) {
    const basicInfoHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">任务名称</h6>
                <p class="mb-3">${summary.job_name || '-'}</p>
                
                <h6 class="text-primary">采集任务ID</h6>
                <p class="mb-3">${summary.runid || '-'}</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">服务器</h6>
                <p class="mb-3">
                    <span class="badge bg-primary">${summary.computer || '-'}</span>
                </p>
                
                <h6 class="text-primary">文件夹</h6>
                <p class="mb-3">
                    <span class="badge bg-secondary">${summary.file || '-'}</span>
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">首次运行时间</h6>
                <p class="mb-3">${formatDateTime(summary.first_run_time)}</p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">最近运行时间</h6>
                <p class="mb-3">${formatDateTime(summary.last_run_time)}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-primary">总运行次数</h6>
                <p class="mb-0">
                    <span class="badge bg-info fs-6">${summary.total_runs} 次</span>
                </p>
            </div>
            <div class="col-md-6">
                <h6 class="text-primary">当前显示</h6>
                <p class="mb-0">
                    <span class="badge bg-secondary fs-6">${summary.displayed_runs} 条记录</span>
                    ${summary.is_limited ? '<small class="text-muted ms-2">(已限制显示)</small>' : ''}
                </p>
            </div>
        </div>
    `;
    
    document.getElementById('task-basic-info').innerHTML = basicInfoHtml;
}

// 渲染统计汇总卡片
function renderSummaryCards(summary) {
    const cardsHtml = `
        <div class="col-md-3 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_url_success)}</h4>
                    <p class="card-text">总成功网址数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_url_fail)}</h4>
                    <p class="card-text">总失败网址数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-copy fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_url_repeat)}</h4>
                    <p class="card-text">总重复网址数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_content_success)}</h4>
                    <p class="card-text">总内容成功数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-file-times fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_content_fail)}</h4>
                    <p class="card-text">总内容失败数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-upload fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_output_success)}</h4>
                    <p class="card-text">总发布成功数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                    <h4 class="card-title">${formatNumber(summary.total_output_fail)}</h4>
                    <p class="card-text">总发布失败数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card bg-gradient text-white h-100" style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-center">
                    <i class="fas fa-play fa-2x mb-2"></i>
                    <h4 class="card-title">${summary.total_runs}</h4>
                    <p class="card-text">总运行次数</p>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('summary-cards').innerHTML = cardsHtml;
}

// 渲染限制提示
function renderLimitNotice(summary) {
    const limitNotice = document.getElementById('limit-notice');
    const limitText = document.getElementById('limit-text');

    if (summary.is_limited) {
        limitText.textContent = `为提高加载效率，当前仅显示最新的 ${summary.displayed_runs} 条记录，共有 ${summary.total_runs} 条记录。`;
        showElement('limit-notice');
    } else {
        hideElement('limit-notice');
    }
}

// 渲染历史记录表格
function renderHistoryTable(tasks) {
    const historyCount = document.getElementById('history-count');
    const summary = taskData.summary;

    if (summary.is_limited) {
        historyCount.innerHTML = `显示 ${tasks.length} / ${summary.total_runs} 条记录`;
    } else {
        historyCount.textContent = `共 ${tasks.length} 条记录`;
    }

    if (tasks.length === 0) {
        showElement('empty-history');
        return;
    }
    
    const tbody = document.getElementById('history-tbody');
    const historyHtml = tasks.map((task, index) => {
        const duration = calculateDuration(task.StartTime, task.EndTime);
        const status = getTaskStatus(task);
        
        return `
            <tr>
                <td class="text-center">
                    <span class="badge bg-light text-dark">#${tasks.length - index}</span>
                </td>
                <td>${formatDateTime(task.StartTime)}</td>
                <td>${formatDateTime(task.EndTime)}</td>
                <td>
                    <span class="badge bg-info">${duration}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-success">${formatNumber(task.UrlSuccessCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-danger">${formatNumber(task.UrlFailCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-warning">${formatNumber(task.UrlRepeatCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-primary">${formatNumber(task.ContentSuccessCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-secondary">${formatNumber(task.ContentFailCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-info">${formatNumber(task.OutputSuccessCount)}</span>
                </td>
                <td class="text-center">
                    <span class="badge bg-dark">${formatNumber(task.OutputFailCount)}</span>
                </td>
                <td class="text-center">
                    ${status}
                </td>
            </tr>
        `;
    }).join('');
    
    tbody.innerHTML = historyHtml;
}

// 计算运行时长
function calculateDuration(startTime, endTime) {
    if (!startTime || !endTime) return '-';
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diff = end - start;
    
    if (diff <= 0) return '-';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else if (minutes > 0) {
        return `${minutes}分钟${seconds}秒`;
    } else {
        return `${seconds}秒`;
    }
}

// 获取任务状态
function getTaskStatus(task) {
    if (task.fabu === 1) {
        return '<span class="badge bg-success"><i class="fas fa-check me-1"></i>已完成</span>';
    } else {
        return '<span class="badge bg-warning"><i class="fas fa-clock me-1"></i>未完成</span>';
    }
}

// 显示错误状态
function showErrorState() {
    document.getElementById('task-basic-info').innerHTML = `
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            加载任务详情失败，请稍后重试
        </div>
    `;
    
    document.getElementById('summary-cards').innerHTML = `
        <div class="col-12">
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                加载统计数据失败
            </div>
        </div>
    `;
    
    document.getElementById('history-tbody').innerHTML = '';
    showElement('empty-history');
    
    hideLoading('loading-basic-info');
    hideLoading('loading-history');
}

// 加载更多记录
function loadMoreRecords() {
    if (!taskParams) {
        showToast('参数错误，无法加载更多记录', 'danger');
        return;
    }

    // 增加限制数量
    const newLimit = currentLimit + 100;

    // 显示加载状态
    const loadMoreBtn = document.querySelector('#limit-notice button');
    const originalText = loadMoreBtn.innerHTML;
    loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>加载中...';
    loadMoreBtn.disabled = true;

    // 加载更多数据
    loadTaskDetail(taskParams.computer, taskParams.file, taskParams.runid, newLimit)
        .then(() => {
            currentLimit = newLimit;
            showToast(`已加载更多记录，当前显示 ${taskData.summary.displayed_runs} 条`, 'success');
        })
        .catch(error => {
            console.error('Error loading more records:', error);
            showToast('加载更多记录失败', 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            loadMoreBtn.innerHTML = originalText;
            loadMoreBtn.disabled = false;
        });
}

// 渲染趋势图表
function renderTrendChart(tasks) {
    // 检查ECharts是否加载
    if (typeof echarts === 'undefined') {
        console.error('ECharts library not loaded');
        document.getElementById('chart-loading').innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                图表库加载失败，无法显示趋势图
            </div>
        `;
        return;
    }

    // 初始化图表
    const chartDom = document.getElementById('trend-chart');
    trendChart = echarts.init(chartDom);

    // 准备数据
    const chartData = prepareTrendData(tasks);

    // 配置图表选项
    const option = getTrendChartOption(chartData, currentChartType);

    // 设置图表
    trendChart.setOption(option);

    // 隐藏加载状态
    hideElement('chart-loading');

    // 响应式调整
    window.addEventListener('resize', function() {
        if (trendChart) {
            trendChart.resize();
        }
    });
}

// 准备趋势图数据
function prepareTrendData(tasks) {
    // 按开始时间排序（从早到晚）
    const sortedTasks = [...tasks].sort((a, b) => {
        const timeA = new Date(a.StartTime);
        const timeB = new Date(b.StartTime);
        return timeA - timeB;
    });

    // 提取数据
    const dates = [];
    const urlSuccess = [];
    const urlFail = [];
    const urlRepeat = [];
    const contentSuccess = [];
    const contentFail = [];
    const outputSuccess = [];
    const outputFail = [];

    sortedTasks.forEach(task => {
        // 格式化日期
        const date = new Date(task.StartTime);
        const formattedDate = `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;

        dates.push(formattedDate);
        urlSuccess.push(parseInt(task.UrlSuccessCount) || 0);
        urlFail.push(parseInt(task.UrlFailCount) || 0);
        urlRepeat.push(parseInt(task.UrlRepeatCount) || 0);
        contentSuccess.push(parseInt(task.ContentSuccessCount) || 0);
        contentFail.push(parseInt(task.ContentFailCount) || 0);
        outputSuccess.push(parseInt(task.OutputSuccessCount) || 0);
        outputFail.push(parseInt(task.OutputFailCount) || 0);
    });

    return {
        dates,
        series: [
            { name: '成功网址数', data: urlSuccess, color: '#28a745' },
            { name: '失败网址数', data: urlFail, color: '#dc3545' },
            { name: '重复网址数', data: urlRepeat, color: '#ffc107' },
            { name: '内容成功数', data: contentSuccess, color: '#007bff' },
            { name: '内容失败数', data: contentFail, color: '#6c757d' },
            { name: '发布成功数', data: outputSuccess, color: '#17a2b8' },
            { name: '发布失败数', data: outputFail, color: '#343a40' }
        ]
    };
}

// 获取趋势图配置选项
function getTrendChartOption(chartData, chartType) {
    const isArea = chartType === 'area';

    return {
        title: {
            text: '采集任务运行趋势',
            left: 'center',
            textStyle: {
                fontSize: 16,
                fontWeight: 'bold'
            }
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            },
            formatter: function(params) {
                let tooltip = `<strong>${params[0].axisValue}</strong><br/>`;
                params.forEach(param => {
                    tooltip += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${formatNumber(param.value)}<br/>`;
                });
                return tooltip;
            }
        },
        legend: {
            data: chartData.series.map(s => s.name),
            top: 30,
            type: 'scroll'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true
        },
        toolbox: {
            feature: {
                saveAsImage: {
                    title: '保存为图片'
                },
                dataZoom: {
                    title: {
                        zoom: '区域缩放',
                        back: '还原缩放'
                    }
                },
                restore: {
                    title: '还原'
                }
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: chartData.dates,
            axisLabel: {
                rotate: 45,
                fontSize: 10
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    if (value >= 1000) {
                        return (value / 1000).toFixed(1) + 'k';
                    }
                    return value;
                }
            }
        },
        dataZoom: [
            {
                type: 'inside',
                start: 0,
                end: 100
            },
            {
                start: 0,
                end: 100,
                height: 30,
                bottom: 10
            }
        ],
        series: chartData.series.map(seriesData => ({
            name: seriesData.name,
            type: 'line',
            stack: isArea ? 'Total' : null,
            areaStyle: isArea ? {} : null,
            emphasis: {
                focus: 'series'
            },
            data: seriesData.data,
            itemStyle: {
                color: seriesData.color
            },
            lineStyle: {
                width: isArea ? 1 : 2
            },
            symbol: isArea ? 'none' : 'circle',
            symbolSize: isArea ? 0 : 4
        }))
    };
}

// 切换图表类型
function switchChartType(type) {
    currentChartType = type;

    // 更新按钮状态
    document.querySelectorAll('#btn-area, #btn-line').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(`btn-${type}`).classList.add('active');

    // 重新渲染图表
    if (trendChart && taskData) {
        const chartData = prepareTrendData(taskData.tasks);
        const option = getTrendChartOption(chartData, type);
        trendChart.setOption(option, true);

        showToast(`已切换到${type === 'area' ? '堆叠面积图' : '折线图'}`, 'info');
    }
}
