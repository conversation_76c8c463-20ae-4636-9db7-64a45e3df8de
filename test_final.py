#!/usr/bin/env python3
"""
最终功能测试
"""

import requests
import webbrowser
import time

def test_final():
    """最终功能测试"""
    print("🎉 采集任务运行结果统计平台 - 最终功能测试")
    print("=" * 60)
    
    base_url = "http://localhost:5011"
    
    # 1. 测试首页
    print("\n1. 📊 测试首页统计...")
    try:
        response = requests.get(f"{base_url}/api/statistics")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"   ✅ 总采集站点数: {data['total_sites']}")
            print(f"   ✅ 当天已采集数: {data['today_collected']}")
            print(f"   ✅ 未采集统计: {data['not_collected']}")
        else:
            print(f"   ❌ 首页API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 首页测试失败: {e}")
    
    # 2. 测试任务列表
    print("\n2. 📋 测试任务列表...")
    try:
        response = requests.get(f"{base_url}/api/tasks?per_page=5")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"   ✅ 总任务数: {data['pagination']['total']}")
            print(f"   ✅ 当前显示: {len(data['tasks'])} 条")
            
            # 获取第一个任务用于详情测试
            if data['tasks']:
                first_task = data['tasks'][0]
                computer = first_task['computer']
                file_folder = first_task['file']
                runid = first_task['runid']
                print(f"   📝 测试任务: {first_task['JobName']}")
                
                # 3. 测试任务详情（新布局）
                print("\n3. 📈 测试任务详情（新布局）...")
                detail_response = requests.get(f"{base_url}/api/task-detail?computer={computer}&file={file_folder}&runid={runid}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()['data']
                    summary = detail_data['summary']
                    
                    print(f"   ✅ 任务名称: {summary['job_name']}")
                    print(f"   ✅ 总运行次数: {summary['total_runs']}")
                    print(f"   ✅ 当前显示: {summary['displayed_runs']} 条记录")
                    
                    if summary['is_limited']:
                        print(f"   ⚡ 性能优化: 限制显示最新 {summary['limit']} 条")
                    
                    # 验证趋势图数据
                    tasks = detail_data['tasks']
                    if len(tasks) >= 2:
                        print(f"   📊 趋势图数据: {len(tasks)} 个数据点")
                        
                        # 检查数据完整性
                        complete_count = 0
                        for task in tasks:
                            if all([
                                task.get('UrlSuccessCount') is not None,
                                task.get('UrlFailCount') is not None,
                                task.get('UrlRepeatCount') is not None,
                                task.get('ContentSuccessCount') is not None,
                                task.get('ContentFailCount') is not None,
                                task.get('OutputSuccessCount') is not None,
                                task.get('OutputFailCount') is not None,
                                task.get('StartTime') is not None
                            ]):
                                complete_count += 1
                        
                        print(f"   ✅ 数据完整性: {complete_count}/{len(tasks)} ({complete_count/len(tasks)*100:.1f}%)")
                        
                        if complete_count == len(tasks):
                            print("   🎯 趋势图数据完整，可正常显示")
                        else:
                            print("   ⚠️ 部分数据不完整，可能影响趋势图")
                    else:
                        print("   ⚠️ 数据点较少，趋势图效果有限")
                
                else:
                    print(f"   ❌ 任务详情API失败: {detail_response.status_code}")
            
        else:
            print(f"   ❌ 任务列表API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 任务列表测试失败: {e}")
    
    # 4. 测试页面布局
    print("\n4. 🎨 测试页面布局...")
    try:
        page_response = requests.get(f"{base_url}/task-detail?computer=服务器A&file=文件夹1&runid=1000")
        if page_response.status_code == 200:
            content = page_response.text
            
            # 检查关键组件
            checks = [
                ("ECharts库", "echarts.min.js" in content),
                ("趋势图容器", "trend-chart" in content),
                ("图表切换按钮", "switchChartType" in content),
                ("统计汇总", "统计汇总" in content),
                ("运行历史", "运行历史记录" in content)
            ]
            
            for name, result in checks:
                status = "✅" if result else "❌"
                print(f"   {status} {name}: {'正常' if result else '缺失'}")
            
            print("   🎯 页面布局顺序: 基本信息 → 趋势图 → 统计汇总 → 历史记录")
            
        else:
            print(f"   ❌ 页面访问失败: {page_response.status_code}")
    except Exception as e:
        print(f"   ❌ 页面布局测试失败: {e}")
    
    # 5. 功能总结
    print("\n" + "=" * 60)
    print("🎊 功能测试完成！")
    print("\n📋 平台功能总览:")
    print("   📊 首页统计 - 总览采集站点和运行状态")
    print("   📋 任务列表 - 分页显示、筛选排序、导出功能")
    print("   📈 任务详情 - 新布局优化，趋势图前置显示")
    print("   🎯 趋势图表 - ECharts堆叠面积图，7个数据系列")
    print("   ⚡ 性能优化 - 记录限制、分页加载")
    
    print(f"\n🌐 访问地址:")
    print(f"   首页: {base_url}")
    print(f"   任务列表: {base_url}/list")
    print(f"   任务详情: {base_url}/task-detail?computer=服务器A&file=文件夹1&runid=1000")
    
    # 询问是否打开浏览器
    try:
        choice = input("\n是否打开浏览器查看任务详情页面？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            print("🌐 正在打开任务详情页面...")
            webbrowser.open(f"{base_url}/task-detail?computer=服务器A&file=文件夹1&runid=1000")
    except KeyboardInterrupt:
        print("\n👋 测试结束")

if __name__ == '__main__':
    test_final()
