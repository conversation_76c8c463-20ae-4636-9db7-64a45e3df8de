from flask import Flask, render_template
from flask_cors import CORS
from config import Config
from database import db

# 创建Flask应用实例
app = Flask(__name__)
app.config.from_object(Config)

# 初始化扩展
db.init_app(app)
CORS(app)

# 导入路由
from routes import api_bp

# 注册蓝图
app.register_blueprint(api_bp, url_prefix='/api')

# 主页路由
@app.route('/')
def index():
    """首页"""
    return render_template('index.html')

@app.route('/list')
def task_list():
    """任务列表页"""
    return render_template('list.html')

@app.route('/task-detail')
def task_detail():
    """任务详情页"""
    return render_template('task_detail.html')

# 错误处理
@app.errorhandler(404)
def not_found(e):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(e):
    db.session.rollback()
    return render_template('500.html'), 500

if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    # 启动应用
    app.run(
        host='0.0.0.0',
        port=app.config['PORT'],
        debug=app.config['DEBUG']
    )
