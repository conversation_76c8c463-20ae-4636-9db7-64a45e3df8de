#!/bin/bash

echo "启动采集任务统计平台..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.7+"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖..."
if ! python3 -c "import flask" &> /dev/null; then
    echo "正在安装依赖..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖安装失败"
        exit 1
    fi
fi

# 检查数据库是否初始化
if [ ! -f "caiji.db" ]; then
    echo "正在初始化数据库..."
    python3 init_db.py
    if [ $? -ne 0 ]; then
        echo "错误: 数据库初始化失败"
        exit 1
    fi
fi

# 启动应用
echo "启动应用..."
echo "应用将在 http://localhost:5011 启动"
echo "按 Ctrl+C 停止应用"
echo
python3 app.py
