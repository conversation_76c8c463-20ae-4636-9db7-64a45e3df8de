#!/usr/bin/env python3
"""
采集任务运行结果统计平台功能演示
"""

import webbrowser
import time
import requests

def demo():
    """演示平台功能"""
    base_url = "http://localhost:5011"
    
    print("🎉 欢迎使用采集任务运行结果统计平台！")
    print("=" * 50)
    
    # 检查服务是否运行
    try:
        response = requests.get(f"{base_url}/api/statistics", timeout=5)
        if response.status_code != 200:
            print("❌ 服务未运行，请先启动应用：python app.py")
            return
    except:
        print("❌ 无法连接到服务，请确保应用正在运行（端口5011）")
        return
    
    print("✅ 服务运行正常，开始功能演示...\n")
    
    # 功能演示菜单
    while True:
        print("\n📋 功能演示菜单：")
        print("1. 📊 首页统计展示")
        print("2. 📋 任务列表管理")
        print("3. 📈 任务详情统计（含趋势图）")
        print("4. 🧪 API接口测试")
        print("5. 📚 功能特性说明")
        print("0. 退出演示")
        
        choice = input("\n请选择功能（0-5）: ").strip()
        
        if choice == '0':
            print("\n👋 感谢使用采集任务运行结果统计平台！")
            break
        elif choice == '1':
            demo_homepage()
        elif choice == '2':
            demo_task_list()
        elif choice == '3':
            demo_task_detail()
        elif choice == '4':
            demo_api_test()
        elif choice == '5':
            show_features()
        else:
            print("❌ 无效选择，请重新输入")

def demo_homepage():
    """演示首页功能"""
    print("\n📊 首页统计展示")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:5011/api/statistics")
        if response.status_code == 200:
            data = response.json()['data']
            print(f"✅ 总采集站点数: {data['total_sites']}")
            print(f"✅ 当天已采集数: {data['today_collected']}")
            print(f"✅ 未采集统计: {data['not_collected']}")
        
        print("\n🌐 正在打开首页...")
        webbrowser.open("http://localhost:5011")
        
    except Exception as e:
        print(f"❌ 获取统计数据失败: {e}")

def demo_task_list():
    """演示任务列表功能"""
    print("\n📋 任务列表管理")
    print("-" * 30)
    
    try:
        response = requests.get("http://localhost:5011/api/tasks?per_page=5")
        if response.status_code == 200:
            data = response.json()['data']
            tasks = data['tasks']
            pagination = data['pagination']
            
            print(f"✅ 总任务数: {pagination['total']}")
            print(f"✅ 当前显示: {len(tasks)} 条记录")
            print("\n📝 最新任务:")
            
            for i, task in enumerate(tasks[:3], 1):
                print(f"  {i}. {task['JobName']} - {task['computer']}/{task['file']}")
                print(f"     成功: {task['UrlSuccessCount']}, 失败: {task['UrlFailCount']}")
        
        print("\n🌐 正在打开任务列表页...")
        webbrowser.open("http://localhost:5011/list")
        
    except Exception as e:
        print(f"❌ 获取任务列表失败: {e}")

def demo_task_detail():
    """演示任务详情功能"""
    print("\n📈 任务详情统计（含趋势图）")
    print("-" * 30)
    
    try:
        # 获取第一个任务的详情
        response = requests.get("http://localhost:5011/api/task-detail?computer=服务器A&file=文件夹1&runid=1000")
        if response.status_code == 200:
            data = response.json()['data']
            summary = data['summary']
            
            print(f"✅ 任务名称: {summary['job_name']}")
            print(f"✅ 总运行次数: {summary['total_runs']}")
            print(f"✅ 当前显示: {summary['displayed_runs']} 条记录")
            print(f"✅ 总成功网址数: {summary['total_url_success']}")
            print(f"✅ 总失败网址数: {summary['total_url_fail']}")
            
            if summary['is_limited']:
                print(f"⚡ 性能优化: 默认显示最新 {summary['limit']} 条记录")
            
            print("\n📊 趋势图功能:")
            print("  • 堆叠面积图和折线图两种模式")
            print("  • 7个数据系列的运行趋势")
            print("  • 支持数据缩放、保存图片等交互")
        
        print("\n🌐 正在打开任务详情页（含趋势图）...")
        webbrowser.open("http://localhost:5011/task-detail?computer=服务器A&file=文件夹1&runid=1000")
        
    except Exception as e:
        print(f"❌ 获取任务详情失败: {e}")

def demo_api_test():
    """演示API接口"""
    print("\n🧪 API接口测试")
    print("-" * 30)
    
    apis = [
        ("统计数据接口", "/api/statistics"),
        ("任务列表接口", "/api/tasks?per_page=3"),
        ("任务详情接口", "/api/task-detail?computer=服务器A&file=文件夹1&runid=1000&limit=5"),
        ("筛选选项接口", "/api/filters/options")
    ]
    
    for name, endpoint in apis:
        try:
            response = requests.get(f"http://localhost:5011{endpoint}")
            if response.status_code == 200:
                print(f"✅ {name}: 正常")
            else:
                print(f"❌ {name}: 错误 {response.status_code}")
        except Exception as e:
            print(f"❌ {name}: 异常 {e}")

def show_features():
    """显示功能特性"""
    print("\n📚 平台功能特性")
    print("=" * 50)
    
    features = [
        "📊 首页统计",
        "  • 总采集站点数统计（根据paichong去重）",
        "  • 当天已采集数统计",
        "  • 未采集统计",
        "  • 最近任务预览",
        "",
        "📋 任务列表管理",
        "  • 分页显示（支持20/50/100条每页）",
        "  • 多条件筛选：任务名称、时间区间、服务器、文件夹",
        "  • 多字段排序：开始时间、结束时间、距今时间",
        "  • 数据导出功能（CSV格式）",
        "  • 任务详情查看：点击查看详情按钮",
        "",
        "📈 任务详情统计",
        "  • 根据computer、file、runid确定同一采集任务",
        "  • 任务基本信息：名称、服务器、文件夹、运行次数",
        "  • 统计汇总：基于所有记录计算总成功/失败数",
        "  • 运行历史：默认显示最新100条记录（性能优化）",
        "  • 分页加载：支持加载更多历史记录",
        "  • 运行趋势图：ECharts堆叠面积图",
        "    - 7个数据系列：成功网址数、失败网址数、重复网址数",
        "                   内容成功数、内容失败数、发布成功数、发布失败数",
        "    - 支持堆叠面积图和折线图切换",
        "    - 交互功能：数据缩放、保存图片、图例筛选",
        "",
        "🛠️ 技术特性",
        "  • 前后端分离架构",
        "  • 响应式设计（Bootstrap 5）",
        "  • RESTful API接口",
        "  • 数据库支持：MySQL/SQLite",
        "  • 性能优化：记录限制、分页加载",
        "  • 可视化图表：ECharts集成"
    ]
    
    for feature in features:
        print(feature)

if __name__ == '__main__':
    demo()
