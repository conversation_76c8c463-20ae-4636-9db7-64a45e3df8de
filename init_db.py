#!/usr/bin/env python3
"""
数据库初始化脚本
"""

from app import app
from database import db
from models import CollectionTask
from datetime import datetime, timedelta
import random

def create_tables():
    """创建数据库表"""
    with app.app_context():
        db.create_all()
        print("数据库表创建成功！")

def create_sample_data():
    """创建示例数据"""
    with app.app_context():
        # 清空现有数据
        CollectionTask.query.delete()
        db.session.commit()
        print("已清空现有数据")
        
        # 创建示例数据
        sample_tasks = []
        servers = ['服务器A', '服务器B', '服务器C']
        folders = ['文件夹1', '文件夹2', '文件夹3', '文件夹4']

        # 创建基础任务
        for i in range(30):
            start_time = datetime.now() - timedelta(days=random.randint(0, 30),
                                                   hours=random.randint(0, 23),
                                                   minutes=random.randint(0, 59))
            end_time = start_time + timedelta(hours=random.randint(1, 8))

            computer = random.choice(servers)
            file_folder = random.choice(folders)

            task = CollectionTask(
                runid=1000 + i,
                JobName=f'采集任务_{i+1}',
                JobId=100 + i,
                UrlSuccessCount=str(random.randint(100, 1000)),
                UrlFailCount=str(random.randint(0, 50)),
                UrlRepeatCount=str(random.randint(0, 100)),
                ContentSuccessCount=str(random.randint(80, 900)),
                ContentFailCount=str(random.randint(0, 20)),
                OutputSuccessCount=str(random.randint(70, 800)),
                OutputFailCount=str(random.randint(0, 10)),
                StartTime=start_time,
                EndTime=end_time,
                computer=computer,
                file=file_folder,
                paichong=f'{100 + i}_{computer}_{file_folder}',
                fabu=random.randint(0, 1)
            )
            sample_tasks.append(task)

        # 为前几个任务创建多次运行记录
        for i in range(3):
            base_runid = 1000 + i
            computer = servers[i % len(servers)]
            file_folder = folders[i % len(folders)]
            job_name = f'采集任务_{i+1}'

            # 为前3个任务创建大量运行记录来测试分页功能
            if i == 0:
                run_count = 150  # 第一个任务创建150条记录
            elif i == 1:
                run_count = 80   # 第二个任务创建80条记录
            else:
                run_count = 50   # 第三个任务创建50条记录

            for j in range(run_count):
                # 为了测试排序，让时间更分散
                start_time = datetime.now() - timedelta(days=random.randint(0, 60),
                                                       hours=random.randint(0, 23),
                                                       minutes=random.randint(0, 59))
                end_time = start_time + timedelta(hours=random.randint(1, 6))

                task = CollectionTask(
                    runid=base_runid,  # 相同的runid
                    JobName=job_name,
                    JobId=100 + i,
                    UrlSuccessCount=str(random.randint(100, 1000)),
                    UrlFailCount=str(random.randint(0, 50)),
                    UrlRepeatCount=str(random.randint(0, 100)),
                    ContentSuccessCount=str(random.randint(80, 900)),
                    ContentFailCount=str(random.randint(0, 20)),
                    OutputSuccessCount=str(random.randint(70, 800)),
                    OutputFailCount=str(random.randint(0, 10)),
                    StartTime=start_time,
                    EndTime=end_time,
                    computer=computer,  # 相同的computer
                    file=file_folder,   # 相同的file
                    paichong=f'{100 + i}_{computer}_{file_folder}',
                    fabu=random.randint(0, 1)
                )
                sample_tasks.append(task)
        
        # 批量插入
        db.session.add_all(sample_tasks)
        db.session.commit()
        print(f"成功创建 {len(sample_tasks)} 条示例数据！")

def main():
    """主函数"""
    print("开始初始化数据库...")
    
    # 创建表
    create_tables()
    
    # 创建示例数据
    create_sample_data()
    
    print("数据库初始化完成！")

if __name__ == '__main__':
    main()
